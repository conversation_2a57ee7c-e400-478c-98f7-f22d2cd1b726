<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTarificationPerformanceIndexes extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('offer_grid_details', function (Blueprint $table) {
            // Composite index for location filtering
            $table->index(['destinationable_id', 'originable_id'], 'idx_offer_grid_details_location');
            
            // Index for offerable polymorphic relationship
            $table->index(['offerable_type', 'offerable_id'], 'idx_offer_grid_details_offerable');
            
            // Index for rubric filtering
            $table->index('rubric_id', 'idx_offer_grid_details_rubric');
            
            // Index for product category filtering
            $table->index('type_product_category', 'idx_offer_grid_details_product_category');
            
            // Composite index for range queries
            $table->index(['min', 'max'], 'idx_offer_grid_details_range');
            
            // Composite index for complete tarification query
            $table->index([
                'offerable_type', 
                'offerable_id', 
                'rubric_id', 
                'destinationable_id', 
                'originable_id'
            ], 'idx_offer_grid_details_tarification');
        });

        Schema::table('offers', function (Blueprint $table) {
            // Composite index for customer and status filtering
            $table->index(['id_customer', 'status'], 'idx_offers_customer_status');
        });

        Schema::table('grids', function (Blueprint $table) {
            // Composite index for type and activation status
            $table->index(['type', 'is_activated'], 'idx_grids_type_activated');
        });

        Schema::table('expedition_tarif_details', function (Blueprint $table) {
            // Index for expedition filtering
            $table->index('expedition_id', 'idx_expedition_tarif_details_expedition');
            
            // Index for customer filtering
            $table->index('customer_id', 'idx_expedition_tarif_details_customer');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('offer_grid_details', function (Blueprint $table) {
            $table->dropIndex('idx_offer_grid_details_location');
            $table->dropIndex('idx_offer_grid_details_offerable');
            $table->dropIndex('idx_offer_grid_details_rubric');
            $table->dropIndex('idx_offer_grid_details_product_category');
            $table->dropIndex('idx_offer_grid_details_range');
            $table->dropIndex('idx_offer_grid_details_tarification');
        });

        Schema::table('offers', function (Blueprint $table) {
            $table->dropIndex('idx_offers_customer_status');
        });

        Schema::table('grids', function (Blueprint $table) {
            $table->dropIndex('idx_grids_type_activated');
        });

        Schema::table('expedition_tarif_details', function (Blueprint $table) {
            $table->dropIndex('idx_expedition_tarif_details_expedition');
            $table->dropIndex('idx_expedition_tarif_details_customer');
        });
    }
}
