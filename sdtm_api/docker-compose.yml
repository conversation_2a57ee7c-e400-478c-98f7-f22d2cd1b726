version: "3.8"

networks:
  app-network:
    driver: bridge

services:
  sdtm_server:
    container_name: 'sdtm_server'
    build: .
    volumes:
      - './:/var/www/html'
      - '/var/www/html/vendor'
    ports:
      - "8000:80"
    depends_on:
      - sdtm_db
      # - elasticsearch
    networks:
      - app-network
    restart: always
  sdtm_db:
    image: postgis/postgis:11-2.5
    container_name: 'sdtm_db'
    restart: always
    ports:
      - "54321:5432"
    environment:
      POSTGRES_PASSWORD: adminsdtm
    networks:
      - app-network
  pgadmin:
    image: dpage/pgadmin4
    container_name: 'pgadmin'
    restart: always
    ports:
      - "5050:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: adminsdtm
    depends_on:
      - sdtm_db
    networks:
      - app-network
  # elasticsearch:
  #   image: elasticsearch:7.9.1
  #   container_name: 'elasticsearch'
  #   environment:
  #     - ELASTIC_PASSWORD=adminsdtm
  #     - discovery.type=single-node
  #     - xpack.security.enabled=true
  #     - path.data=/usr/share/elasticsearch/data
  #     - bootstrap.memory_lock=true
  #     - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
  #   ulimits:
  #     memlock:
  #       soft: -1
  #       hard: -1
  #     nofile:
  #       soft: 65536
  #       hard: 65536
  #   ports:
  #     - "9200:9200"
  #     - "9300:9300"
  #   networks:
  #     - app-network
  # kibana:
  #   platform: linux/x86_64
  #   image: kibana:7.9.1
  #   container_name: 'kibana'
  #   ports:
  #     - "5601:5601"
  #   environment:
  #     - ELASTICSEARCH_USERNAME=elastic
  #     - ELASTICSEARCH_PASSWORD="adminsdtm"
  #     - ELASTICSEARCH_URL=http://elasticsearch:9200
  #     - SERVER_NAME=my-kibana
  #     - XPACK_SECURITY_ENABLED=true
  #     - PATH_DATA=/usr/share/kibana/data
  #   depends_on:
  #     - elasticsearch
  #   networks:
  #     - app-network
  redis:
    image: bitnami/redis:latest
    container_name: 'redis'
    restart: always
    ports:
      - '6379:6379'
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    volumes:
      - './cache:/data'
    networks:
      - app-network
  # rabbitmq:
  #   networks:
  #     - app-network
  #   hostname: 'rabbitmq'
  #   image: rabbitmq:3.11.3-management-alpine
  #   container_name: 'rabbitmq'
  #   ports:
  #       - 5672:5672
  #       - 15672:15672

