<?php

namespace App\Modules\Expedition\Helpers;
use Illuminate\Support\Facades\Config;
use App\Enums\eGridLocationType;
use App\Enums\eTaxeValType;
use Log;
use DB;

class TarificationHelper {


    public static function calcul_tarification($exp, $tarifs, $rubric, $item) {
    Log::info('------> calcul_tarification');
    Log::info($rubric);
    Log::info($tarifs);

    Log::info('expedition_in_tarification'.json_encode($exp));

    if (!count($tarifs)) return [0, 0, 0];

    $tarifs = array_values($tarifs->toArray());
    $tarifs = $tarifs[0];

    $rubric_value = 0;
    if ($rubric->value) {
        $rubric_value = isset($exp[$rubric->value]) ? $exp[$rubric->value] : (isset($item[$rubric->value]) ? $item[$rubric->value] : 0);
    }

    $is_coef = $tarifs['type_val'] == eTaxeValType::_COEF;
    $calcul_basis = $tarifs['basis_calcul']['title'];
    $val_fact = self::{'get_'.$calcul_basis}($exp, $item, $rubric);
    $init_nb = self::{'get_'.$calcul_basis.'_NB'}($exp, $item, $rubric);

    $calcul_val = 0;

    if ($is_coef) {
        $calcul_val = $tarifs['calcul_val'] * (double) $rubric_value * ((int) ($rubric_value >= $tarifs['min']));
    } else if ($tarifs['type_val'] == eTaxeValType::_PRICE && $tarifs['min'] != $tarifs['max']) {
        if ($tarifs['basis_calcul']['title'] == 'COLIS_POIDS') {
            $volumetric_weight = $exp['volumetric_weight'] ?? 0;
            $val_fact = max($val_fact, $volumetric_weight);

            $max_value = $tarifs['max'] ?? 0;
            $calcul_val = $tarifs['calcul_val'] * (
                $init_nb === $val_fact && $tarifs['calcul_basis_id'] && $max_value && $val_fact > $max_value
                ? $max_value
                : $val_fact
            );

            if ($tarifs['min_by_package']) {
                $calcul_val = max($tarifs['min_by_package'], $calcul_val);
            }

            $calcul_val *= $init_nb;
        } else {
            $calcul_val = $tarifs['calcul_val'] * (
                $init_nb === $val_fact && $tarifs['calcul_basis_id'] && $tarifs['max'] && $init_nb > $tarifs['max']
                ? $tarifs['max']
                : $init_nb
            );
        }
    } else {
        $calcul_val = $tarifs['calcul_val'];
    }

    // Avoid divide by zero
    $tarifs['min'] = $tarifs['min'] == 0 ? 1 : $tarifs['min'];
    $max = $tarifs['max'] ?? 9999999999999;
    $max = $max == 0 ? 1 : $max; // Ensure max is not zero

    $max_poids = $tarifs['max_poids'] ?? 0;
    $cond_max = $max_poids + ($max * ((int) ($max / max(1, $max_poids + $max)))); // protect division

    $sup_fact_calc = 0;
    if (!empty($tarifs['sup_basis_calcul'])) {
        $calcul_basis = $tarifs['sup_basis_calcul']['title'];
        $sup_fact_calc = self::{'get_'.$calcul_basis.'_NB'}($exp, $item, $rubric);
    }

    $nb = ($tarifs['sup_calcul_basis_id'] && $tarifs['calcul_basis_id'] &&
           $tarifs['calcul_basis_id'] == $tarifs['sup_calcul_basis_id'] &&
           $init_nb === $val_fact) ? 1 : $init_nb;

    // Final calculation
    $base_calculation = $val_fact / max(1, $tarifs['min']); // prevent divide by zero
    $factor = (int) ($tarifs['min'] / max(1, $max));
    $additional_units = ceil(($max - $tarifs['min']) / max(1, $max)); // prevent divide by zero

    $montant = $calcul_val * ($base_calculation * $factor + $additional_units);

    $sup_factor = 0;
    if ($sup_fact_calc > $cond_max && $tarifs['u_sup'] != 0) {
        $sup_factor = ($sup_fact_calc - $cond_max) * ($tarifs['val_sup'] / max(1, $tarifs['u_sup']));
        $montant += $sup_factor * $nb;
    }

    $val_min = ($tarifs['type_val'] == eTaxeValType::_COEF && $tarifs['min'] && $rubric_value <= $tarifs['min'])
        ? 0
        : $tarifs['min_val'];

    return [$montant, $val_min, $tarifs['max_val']];
}


    /**
     * calcul dynamique des tarification selon base de calcul, type de valeur (prix, coef ou prix par tranche) et valeur supp par unité.
     * @param Expedition $exp: expedition en question
     * @param GridDetails $tarifs: la ligne ou les lignes tarifaire qui vérifies les conditions (destination, tranche, ...)
     * @param Rubric rubric à calculer ses tarifs
     * @param ExpeditionItem item en question dans le cas du transport sinon null
     * @return array [montant calculé, la valeur min associé, valeur max]
     */
    public static function calcul_tarification_old($exp, $tarifs, $rubric, $item) {
        Log::info('------> calcul_tarification');
        Log::info($rubric);
        Log::info($tarifs);

        Log::info('expedition_in_tarification'.json_encode($exp));

        
        if(!count($tarifs)) return [0, 0, 0]; // Si on a aucun tarifs (gridDetails) return des valeurs 0
        $tarifs = array_values($tarifs->toArray());
        $tarifs = $tarifs[0]; // get tarif
        $rubric_value =  $exp[$rubric->value]; // get la valeur associé à la rubric --> montant du font ou valeur déclaré ...
        Log::info('rubric_value ---> '. $rubric_value);

        $is_coef = $tarifs['type_val'] == eTaxeValType::_COEF;
        $calcul_basis = $tarifs['basis_calcul']['title']; // base de calcul --> COLIS - COLIS_POIDS ....
        $val_fact = self::{'get_'.$calcul_basis}($exp, $item, $rubric); // get la valeur associé à la base de calcul
        $init_nb = self::{'get_'.$calcul_basis.'_NB'}($exp, $item, $rubric); // get le nombre des unité selon la base de calcule (nombre des colis - nombre des bl ...)
        Log::info('val factor ---> '. $val_fact);
        Log::info('ini nb ---->'. $init_nb );
        Log::info('tarif max ----> '. $tarifs['max']);
        Log::info('nb first --- '.($init_nb === $val_fact &&  $tarifs['max'] && $init_nb > $tarifs['max'] ? $tarifs['max'] : $init_nb));
        Log::info('-----> calcul_val (prix) ---> '.$tarifs['calcul_val']);

        /**
         * Si type val est coef ==> on fait la multiplication du pourcentage saisie dans la condition avec la valeur associé de la rubrique
         * Sinon si le type val est price et min != max alors on parle de condition est par tranche mais le prix est par unité (exp: 25 dh/colis pour les colis de 1 à 25 kg ); dans ce cas on fait la multiplication de prix avec le nombre des colis
         * Sinon prix par tranche 
         */
        $calcul_val = $is_coef ? 
        ($tarifs['calcul_val'] * $rubric_value * ((int) ($rubric_value >= $tarifs['min'])) ): 
            ($tarifs['type_val'] == eTaxeValType::_PRICE && $tarifs['min']!=$tarifs['max'] ? $tarifs['calcul_val']*($init_nb === $val_fact && $tarifs['calcul_basis_id']  &&  $tarifs['max'] && $init_nb > $tarifs['max'] ? $tarifs['max'] : $init_nb) : $tarifs['calcul_val']);
        $tarifs['min'] = $tarifs['min'] == 0 ? 1 : $tarifs['min'];
        $max = $tarifs['max'] == null ? 9999999999999 : $tarifs['max'];
        $cond_max = $tarifs['max_poids'] + ($max * ((int) ($max/ ($tarifs['max_poids'] + $max)) )); // si max_poids exist return max_poids sinon max

        $sup_fact_calc = 0;
        // get valeur de base de calcul pour vel suo s'elle existe
        if($tarifs['sup_basis_calcul']) {
            $calcul_basis_sup = $tarifs['sup_basis_calcul']['title'];
            $sup_fact_calc = self::{'get_'.$calcul_basis_sup}($exp, $item, $rubric);

        }
        
        $nb =  $tarifs['sup_calcul_basis_id'] && $tarifs['calcul_basis_id'] && $tarifs['calcul_basis_id'] == $tarifs['sup_calcul_basis_id'] && $init_nb === $val_fact ? 1 : $init_nb; 
        //
        // Log::info('calcul_val ---> '.$calcul_val);
        // Log::info('val supp -----> '. $tarifs['val_sup'] / $tarifs['u_sup']);
        // Log::info('nb ---> '. $nb);
        // Log::info('init_nb ---> '. $init_nb);
        // Log::info('$sup_fact_calc '. $sup_fact_calc);
        // Log::info('$cond_max '. $cond_max);
        // Log::info('supp amount '. ($tarifs['val_sup'] / $tarifs['u_sup']) * ((int) ($sup_fact_calc > $cond_max)));

        $montant = $calcul_val * ( ($val_fact/$tarifs['min'])*((int) ($tarifs['min']/$max)) + ceil(($max - $tarifs['min'])/$max) ) + 
        (($sup_fact_calc - $cond_max) * ($tarifs['val_sup'] / $tarifs['u_sup']) * ((int) ($sup_fact_calc > $cond_max))) * $nb; //0
        $val_min = $tarifs['type_val'] == eTaxeValType::_COEF && $tarifs['min'] && $rubric_value <= $tarifs['min'] ? 0 : $tarifs['min_val'];
        return [$montant, $val_min, $tarifs['max_val']];
    }


    /**
     * recherche des conditions par categorie du produit 
     */
    public static function filterByCategory($details, $exp, $item, $rubric) {
        Log::info('filterByCategory----');
        if(!$item) return [];

        // TODO: to be fixed 
        $prod_category_id = $item['prod_category_id'] ?? $item["product_category_id"] ?? null;
        $data = $details->filter(function ($value, $key) use($item , $prod_category_id){
            return $value->prod_category_id == $prod_category_id && $prod_category_id != null;
        });
        Log::info('filterByCategory result count: ' . $data->count());
        return $data;
    }



    public static function filterByCity($details, $exp, $item, $rubric) {
        Log::info('------> filterByCity');
        $data = $details->filter(function ($value, $key) use($exp) {
            return $value->destinationable_id == $exp['dest_city_id'];
        });
        Log::info('filterByCity result count: ' . $data->count());
        return $data;
    }




    public static function filterByAllCategory($details, $exp, $item, $rubric) {
        Log::info('------> filterByAllCategory');
        $data = $details->filter(function ($value, $key) use($item){
            if ($item)  return $value->prod_category_id == null && ($value->type_product_category == $item['type'] ||  $value->type_product_category == null) ;     
        
            return $value->prod_category_id == null ;
        });
        Log::info('filterByAllCategory result count: ' . $data->count());
        return $data;
    }

    public static function filterByClassCities($details, $exp, $item, $rubric) {
        Log::info('------> filterByClassCities');
        $data = $details->filter(function ($value, $key) use($exp) {
            if($value->destinationable_type == eGridLocationType::_CATEGOTIE_CITY) {
                $city_class = DB::select("SELECT * FROM city_inter_categorie_cities WHERE city_id=".$exp['dest_city_id']." AND category_city_id=".$value->destinationable_id);
                return $city_class;
            }    
            return false;
        });
        Log::info('filterByClassCities result count: ' . $data->count());
        return $data;
    }

    public static function filterByAllCities($details, $exp, $item, $rubric) {
        Log::info('------> filterByAllCities');
        // Use whereNull for better performance
        $data = $details->whereNull('destinationable_id');
        Log::info('filterByAllCities result count: ' . $data->count());
        return $data;
    }

    public static function filterByCalculBasis($details, $exp, $item, $rubric) {
        Log::info('------> filterByCalculBasis');    
    
        if(count($details) == 1) {
            Log::info('filterByCalculBasis: Only one detail, returning it');
            return $details;
        }
        
        $calcul_basis = $details->first()->basis_calcul->title;
        $is_coef = $details->first()->type_val == eTaxeValType::_COEF;
        $value = $is_coef ? $exp[$rubric->value] : self::{'get_'.$calcul_basis}($exp, $item, $rubric);

        Log::info('filterByCalculBasis: rubric=' . $rubric->title_affichage . ', value=' . $value);
        
        foreach ($details as $key => $detail) {
            if($detail->min == $detail->max) {
                Log::info('filterByCalculBasis: Found exact match min=max=' . $detail->min);
                return collect([$detail]);
            }
            if($value >= $detail->min && (($detail->max && $value <= $detail->max) || (!$detail->max))) {
                Log::info('filterByCalculBasis: Found range match min=' . $detail->min . ', max=' . $detail->max);
                return collect([$detail]);
            }
        }

        $lastDetail = $details->sortBy('min')->last();
        if ((isset($lastDetail->max_poids) && $lastDetail->max_poids == 0) && $lastDetail->val_sup >= 0 && $lastDetail->u_sup >= 1 && $lastDetail->sup_basis_calcul != null) {
            Log::info('filterByCalculBasis: Using last detail with supplementary calculation');
            return collect([$lastDetail]);
        }
        
        Log::info('filterByCalculBasis: No matching detail found');
        return [];
    }

    public static function filterByProductType($details, $exp, $item) {
        Log::info('------> filterByProductType');
        if(!$item) {
            Log::info('filterByProductType: No item provided');
            return collect();
        }

        // Use where() for better performance
        $data = $details->where('type_product_category', $item['type']);
        Log::info('filterByProductType result count: ' . $data->count());
        return $data;
    }

    public static function get_COLIS($exp, $item, $rubric) {
        Log::info('------> get_COLIS');
        if ($rubric->id != 1) return $exp['number'];
        
        return $item['number'];
    }

    public static function get_COLIS_NB($exp, $item, $rubric) {
        Log::info('------> get_COLIS nb');
        if ($rubric->id != 1) return $exp['number'];
        return $item['number'];
    }

    public static function get_COLIS_POIDS($exp, $item, $rubric) {
        Log::info('------> get_COLIS_POIDS');
        return $item['weight']/$item['number'];
    }

    public static function get_COLIS_POIDS_NB($exp, $item, $rubric) {
        Log::info('------> get_COLIS_POIDS nb');
        return $item['number'];
    }

    public static function get_COLIS_POIDS_TR($exp, $item, $rubric) {
        Log::info('------> get_COLIS_POIDS');
        return $item['weight'];
    }

    public static function get_EXPEDITION($exp, $item, $rubric) {
        Log::info('------> get_EXPEDITION');
        return 1;
    }

    public static function get_EXPEDITION_NB($exp, $item, $rubric) {
        Log::info('------> get_EXPEDITION nb');
        return 1;
    }

    public static function get_EXPEDITION_POIDS($exp, $item, $rubric) {
        Log::info('------> get_EXPEDITION_POIDS');
        return $exp['weight'];
    }

    public static function get_EXPEDITION_POIDS_NB($exp, $item, $rubric) {
        Log::info('------> get_EXPEDITION_POIDS nb');
        return 1;
    }

    public static function get_EXPEDITION_VOLUME($exp, $item, $rubric) {
        Log::info('------> filterByCity');
        return  $exp['volume'];
    }

    public static function get_EXPEDITION_VOLUME_NB($exp, $item, $rubric) {
        Log::info('------> filterByCity');
        return  1;
    }

    public static function get_FACTURE($exp, $item, $rubric) {
        Log::info('------> get_FACTURE');
        return $exp['number_facture'];
    }

    public static function get_FACTURE_NB($exp, $item, $rubric) {
        Log::info('------> get_FACTURE nb'. $exp['number_facture']);
        return $exp['number_facture'];
    }

    public static function get_BL($exp, $item, $rubric) {
        Log::info('------> get_BL');
        return $exp['number_bl'];
    }


    public static function get_BL_NB($exp, $item, $rubric) {
        Log::info('------> get_BL nb');
        return $exp['number_bl'];
    }
     /**
      * recherche des conditions de tarification par les différents facteurs (categorie prod - destination - tranche .....) selon le scenarion configuré ds globalConfig, aussi par niveau offre/template/public grid
      */
    public static function recursive_search($details, $exp, $key, $rubric, $grids, $grid_index = 0, $item) {
        Log::info('********************** recursive_search **************************** '.$grid_index);

        if($key == 'Grid' && $grid_index < count($grids) - 1 && count($details) <= 0) {
            $grid_index++;
            return self::recursive_search($grids[$grid_index], $exp ,Config::get('globalconfig')['tarification_scenarios'][strval(intval(count($grids[$grid_index]) > 0))], $rubric, $grids, $grid_index,  $item);
        } else if($key == 'Grid' && count($details) <= 0) {


            Log::info('last iteration ....');
            Log::info('grid index  ....'.$grid_index);
            Log::info('grid index detqils  ....'.json_encode($details));


            
            return [];}
        else if(($key == 'end' || $key == 'Grid') && count($details)) return $details; 
        $res = self::{$key}($details, $exp, $item, $rubric);
        $new_key = Config::get('globalconfig')['tarification_scenarios'][$key][strval(intval(count($res) > 0))];

        return self::recursive_search((count($res) || $new_key == 'Grid' ? $res : $details), $exp , $new_key, $rubric, $grids, $grid_index, $item);
    }

    /**
     * details des offres - details template - details de la grille publique
     */

    public static function gridsDetails($rubric_details_offers, $rubric_details_template, $rubric_details_public, $rubric) {
        Log::info('------>grids Details');

        // Optimized filtering with early return for empty collections
        if ($rubric_details_offers->isEmpty() && $rubric_details_template->isEmpty() && $rubric_details_public->isEmpty()) {
            return [collect(), collect(), collect()];
        }

        // Use where() instead of filter() for better performance on indexed collections
        $current_details_offer = $rubric_details_offers->where('rubric_id', $rubric->id);
        Log::info('current_details_offer count: ' . $current_details_offer->count());

        $current_details_client_template = collect($rubric_details_template)->where('rubric_id', $rubric->id);
        $current_details_public = collect($rubric_details_public)->where('rubric_id', $rubric->id);

        return [$current_details_offer, $current_details_client_template, $current_details_public];
    }

    public static function detailsExp($expedition) {


        
        Log::info('expeditionii ..'.json_encode($expedition));

        $expedition['number_bl'] = array_sum(
         array_map(function($item) {

            return count($item['references']);
         },array_filter($expedition['return_documents'], function($item) {
            return $item['type'] == 'BL';
        })));

        $expedition['number_facture'] = array_sum(array_map(function($item) {
            return count($item['references']);}, array_filter($expedition['return_documents'], function($item) {
            return $item['type'] == 'Facture';
        })));
        $expedition['number'] = 0;
        $expedition['weight'] = 0;
        $expedition['volume'] = 0;
        $expedition['volumetric_weight'] = 0;



        foreach ($expedition['expedition_items'] as $key => $item) {
            $expedition['weight'] += $item['weight'];
            $expedition['volume'] += $item['volume'] ?? 0;
            $expedition['number'] += $item['number'];
            $expedition['volumetric_weight'] +=  $item['volumetric_weight'] ?? 0;;
        }
        return $expedition;
    }

}
