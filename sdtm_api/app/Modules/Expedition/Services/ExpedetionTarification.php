<?php

namespace App\Modules\Expedition\Services;

use Log;
use App\Enums\eGridType;
use App\Enums\eRespCode;
use App\Jobs\GeneratePDF;
use App\Enums\eNatureTypes;
use App\Enums\eOfferStatus;
use App\Enums\eServiceType;
use App\Traits\UploadTrait;
use App\Enums\eConfigGlobal;
use App\Enums\eDocumentType;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use App\Enums\eAddressesTypes;
use App\Enums\eExpDocumentType;
use App\Enums\eExpeditionStatus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use \App\Modules\Location\Models\City;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Cache;
use App\Modules\Adresse\Models\Adresse;
use App\Modules\ModePort\Models\ModePort;
use Illuminate\Support\Facades\Validator;
use App\Modules\Expedition\Models\Receipet;
use MStaack\LaravelPostgis\Geometries\Point;
use App\Modules\Expedition\Models\Expedition;
use App\Modules\TypeService\Models\TypeService;
use App\Modules\GlobalConfig\Models\GlobalConfig;
use App\Modules\Grid\Repositories\GridRepository;
use App\Modules\TypeDelivery\Models\TypeDelivery;
use App\Modules\Config\Models\Config as ConfigTMS;
use App\Modules\Offer\Repositories\OfferRepository;
use App\Modules\Rubric\Repositories\RubricRepository;
use App\Modules\Expedition\Helpers\TarificationHelper;
use App\Modules\Expedition\Models\ReceipetTarifDetails;
use App\Modules\Package\Repositories\PackageRepository;
use App\Modules\Customer\Repositories\CustomerRepository;
use App\Modules\Document\Repositories\DocumentRepository;
use App\Modules\Expedition\Models\ExpeditionTarifDetails;
use App\Modules\Expedition\Repositories\ExpeditionRepository;
use App\Modules\ReturnDocument\Repositories\ReturnDocumentRepository;
use App\Modules\ExpeditionItems\Repositories\ExpeditionItemsRepository;
use App\Modules\OfferGridDetails\Repositories\OfferGridDetailsRepository;
use App\Modules\Expedition\Services\TarificationCacheService;


class ExpedetionTarification
{

    use ApiResponser, UploadTrait;

    protected $expeditionRepository;
    protected $expiditionItemsRepository;
    protected $packageRepository;
    protected $customerRepository;
    protected $returnDocumentRepository;
    protected $documentRepository;
    protected $offerRepository;
    protected $rubricRepository;
    protected $gridRepository;
    protected $offerGridDetails;
    protected $cacheService;

    public function __construct(
        ExpeditionRepository $expeditionRepository,
        ExpeditionItemsRepository $expiditionItemsRepository,
        PackageRepository $packageRepository,
        CustomerRepository $customerRepository,
        ReturnDocumentRepository $returnDocumentRepository,
        DocumentRepository $documentRepository,
        OfferRepository $offerRepository,
        RubricRepository $rubricRepository,
        GridRepository $gridRepository,
        OfferGridDetailsRepository $offerGridDetails,
        TarificationCacheService $cacheService
    ) {
        $this->expeditionRepository = $expeditionRepository;
        $this->expiditionItemsRepository = $expiditionItemsRepository;
        $this->packageRepository = $packageRepository;
        $this->customerRepository = $customerRepository;
        $this->returnDocumentRepository = $returnDocumentRepository;
        $this->documentRepository = $documentRepository;
        $this->offerRepository = $offerRepository;
        $this->rubricRepository = $rubricRepository;
        $this->gridRepository = $gridRepository;
        $this->offerGridDetails = $offerGridDetails;
        $this->cacheService = $cacheService;
    }



    // service for testing purpose 
    public function tarificationTest(Request $request)
    {

        Log::info("request tarification test" . json_encode($request->all()));
        $response = self::tarification($request);


        Log::info('response_tarification' . json_encode($response));
        //TODO:
        return $response->toJson();
    }



    // public function transformReturnFundType($key){
    //     return $this->return_funds[$key];
    // }

    public function keySwap(array $resource, array $keys)
    {
        $newResource = [];

        foreach ($resource as $k => $r) {
            if (array_key_exists($k, $keys)) {
                $newResource[$keys[$k]] = $r;
            } else {
                $newResource[$k] = $r;
            }
        }

        return $newResource;
    }

    public function tarification($payload, $id_exp = null, $driver_id = null)
    {
        Log::info('in tarif id exp ' . json_encode($id_exp));
        Log::info('response_tarification' . json_encode($payload));

        // Validate cities first
        $dest_city = City::find($payload['dest_city_id']);
        if (!$dest_city)
            return $this->mainResponse(eRespCode::_400_BAD_REQUEST, false, 'dest_city not found');
        $payload['dest_city'] = $dest_city;

        $origin_city = City::find($payload['origin_city_id']);
        if (!$origin_city)
            return $this->mainResponse(eRespCode::_400_BAD_REQUEST, false, 'origin_city not found');
        $payload['origin_city'] = $origin_city;

        // Get mode port with filtered grids
        $payload['mode_port'] = ModePort::where('title', $payload['mode_port'])->first();

        // Get rubrics with caching
        $rubrics = $this->cacheService->getCachedRubrics();

        // Get cached public grid details
        $rubric_details_public = $this->cacheService->getCachedPublicGridDetails($origin_city->id, $dest_city->id);
        
        $payload['rubrics'] = $rubrics;

        if ($payload['id_receiver'] != -1) {

            $receiver = $this->customerRepository->with(['authorized_customers_in_send'])->find($payload['id_receiver']);
            $payload['receiver'] = $receiver;
            if (!$receiver)
                return $this->mainResponse(eRespCode::_400_BAD_REQUEST, false, 'Receiver not found');
        }

        $rejected_rubrics = [];

        $rubric_details_template = collect();
        $offers = collect();
        $rubric_details_offers = collect();

        if ($payload['id_customer'] != -1) {
            // Use cached customer data
            $customerData = $this->cacheService->getCachedCustomerGridDetails(
                $payload['id_customer'],
                $origin_city->id,
                $dest_city->id
            );

            $client = $customerData['client'];
            if (!$client)
                return $this->mainResponse(eRespCode::_400_BAD_REQUEST, false, 'Client not found');

            $payload['client'] = $client;
            $rubric_details_template = $customerData['grid_details'];

            // Get cached offers
            $rubric_details_offers = $this->cacheService->getCachedCustomerOffers(
                $payload['id_customer'],
                $origin_city->id,
                $dest_city->id
            );

            Log::info('offers count: ' . $rubric_details_offers->count());

            if (!$client->use_grid) {
                Log::info('client does not use grid');
                $rubric_details_public = [];
            }

            Log::info('rubric_details_template '.json_encode($rubric_details_template));





        } else if (
            $payload['id_receiver'] != -1
        ) {
            // Use cached receiver data
            $receiverData = $this->cacheService->getCachedCustomerGridDetails(
                $payload['id_receiver'],
                $origin_city->id,
                $dest_city->id
            );

            $receiver = $receiverData['client'];
            $payload['receiver'] = $receiver;

            if ($receiver) {
                $rubric_details_offers = $this->cacheService->getCachedCustomerOffers(
                    $payload['id_receiver'],
                    $origin_city->id,
                    $dest_city->id
                );
                $rubric_details_template = $receiverData['grid_details'];
            }
        }

        $this->lookup($payload, $rejected_rubrics);

        if ($payload['id_customer'] != -1) {

            Log::info('Reloading client data after swap', [
                'id_customer' => $payload['id_customer']
            ]);

            // Clear cache and reload customer data
            $this->cacheService->clearCustomerCache($payload['id_customer']);

            $customerData = $this->cacheService->getCachedCustomerGridDetails(
                $payload['id_customer'],
                $origin_city->id,
                $dest_city->id
            );

            $client = $customerData['client'];
            if (!$client)
                return $this->mainResponse(eRespCode::_400_BAD_REQUEST, false, 'Client not found after swap');

            $payload['client'] = $client;
            $rubric_details_template = $customerData['grid_details'];

            $rubric_details_offers = $this->cacheService->getCachedCustomerOffers(
                $payload['id_customer'],
                $origin_city->id,
                $dest_city->id
            );

            Log::info('Reloaded offers count', ['count' => $rubric_details_offers->count()]);

            if (!$client->use_grid) {
                Log::info('client does not use grid');
                $rubric_details_template = collect();
                $rubric_details_public = collect();
            }
        }

        $services_amounts = [];
        $total_ht = 0;
        $total_tva = 0;
        $total_ttc = 0;

        Log::info('return_fund_array from payload before ' . json_encode($payload['return_funds_amounts']));


        // $payload['return_funds_amounts'] = json_decode($payload['return_funds_amounts'] , true);

        // expected values


        $expected_values = ['C/REMB', 'C/TRAITE', 'C/CHEQUE'];

        // loop through expected values
        foreach ($expected_values as $value) {
            if (!in_array($value, array_column($payload['return_funds_amounts'], 'type'))) {
                // add value with default 0
                $v = [
                    'type' => $value,
                    'amount' => 0,
                ];
                array_push($payload['return_funds_amounts'], $v);
            }
        }

        Log::info('return_fund_array from payload after' . json_encode($payload['return_funds_amounts']));

        $return_funds = ['C/Espèce' => 'C/REMB', 'C/Trait' => 'C/TRAITE', 'C/Chèque' => 'C/CHEQUE'];
        $return_fund_array = [];
        foreach ($payload['return_funds_amounts'] as $item) {
            if (is_array($item)) {
                $return_fund_array[$item['type']] = $item['amount'];
            } else {
                $return_fund_array[$item->type] = $item->amount;
            }
        }

        Log::info('return_fund_array', $return_fund_array);

        Log::info('return_fund_array' . json_encode($return_fund_array));


        $payload['return_funds_amounts'] = $return_fund_array;

        $payload = array_merge($payload, $payload['return_funds_amounts']);
        unset($payload['return_funds_amounts']);

        $tranches = [];
        $exp_details = TarificationHelper::detailsExp($payload);
        // calcul de tarif de chaque rubrique
        foreach ($payload['rubrics'] as $key => $rubric) {

            Log::info('rubric_details_offers' . json_encode($rubric_details_offers));
            $grids_details_by_rubric = TarificationHelper::gridsDetails($rubric_details_offers, $rubric_details_template, $rubric_details_public, $rubric);
            $new_key = Config::get('globalconfig')['tarification_scenarios'][strval(intval(count($grids_details_by_rubric[0]) > 0))];
            if ($rubric->title == 'TRANSPORT' || $rubric->title == 'ADV') { // montant de transport 

                Log::info('title rubric' . $rubric->title);







                $items_amounts = [];
                $all_min = [];
                $all_max = [];
                foreach ($exp_details['expedition_items'] as $key => $item) {
                    $res = TarificationHelper::recursive_search($grids_details_by_rubric[0], $exp_details, $new_key, $rubric, $grids_details_by_rubric, 0, $item);

                    // Log::info('customer_use_grid'.json_encode($client->use_grid));


                    if (!count($res)) {


                        Log::info('rubrique ...' .json_encode($rubric));

                        
                        
                        if ($payload['id_customer'] != -1) {
                            return !$client->use_grid ? $this->mainResponse(eRespCode::_400_TA_ONLY_OFFER, false) : $this->mainResponse(eRespCode::_400_TA_TRANCHE_NOT_FOUND, false);
                        }
                        return $this->mainResponse(eRespCode::_400_TA_TRANCHE_NOT_FOUND, false);
                    }

                    $tranches[] = $res->first();
                    $current_amount = TarificationHelper::calcul_tarification($exp_details, $res, $rubric, $item);
                    $items_amounts[] = $current_amount[0];
                    $all_min[] = $current_amount[1];
                    $all_max[] = $current_amount[2] != null ? $current_amount[2] : INF;
                }
                $amount = array_sum($items_amounts);
                $min_val = max($all_min);
                $max_val = max($all_max);
                $amount = min(max($amount, $min_val), $max_val); // verifier min et max val


                Log::info('rubric....' . $rubric->title);
                Log::info('amount before tax' . $amount);
                // Log::info('taxe_transport'.$configs['taxe_transport']);

                $amount = [
                    'ht' => $amount,
                    'tva' => round($amount * $rubric->taxe, 2),
                    'ttc' => round($amount + ($amount * $rubric->taxe), 2),
                    'taxe' => $rubric->taxe
                ];
                // $amount += $amount*$configs['taxe_transport'];
            } else {


                // montant de chaque service
                //TODO: check
                // if(in_array($rubric->title, ['C/REMB', 'C/TRAITE', 'C/CHEQUE']) && $exp_details['return_funds_type'] != Config::get('globalconfig')['rubric_return_funds_type'][$rubric->title]) { continue; }
                $res = TarificationHelper::recursive_search($grids_details_by_rubric[0], $exp_details, $new_key, $rubric, $grids_details_by_rubric, 0, null);

                if (!count($res)) {
                    Log::info('rubric not found ' . json_encode($rubric->title));

                    if ($payload['id_customer'] != -1) {
                        return !$client->use_grid ? $this->mainResponse(eRespCode::_400_TA_ONLY_OFFER, false) : $this->mainResponse(eRespCode::_400_TA_TRANCHE_NOT_FOUND, false);
                    }
                    return $this->mainResponse(eRespCode::_400_TA_TRANCHE_NOT_FOUND, false);
                }


                $tranches[] = $res->first();
                $amount = TarificationHelper::calcul_tarification($exp_details, $res, $rubric, null);

                Log::info('amount befroe', $amount);
                $amount = min(max($amount[0], $amount[1]), $amount[2] != null ? $amount[2] : INF);
                Log::info('amount after' . $amount);

                $amount = [
                    'ht' => $amount,
                    'tva' => round($amount * $rubric->taxe, 2),
                    'ttc' => round($amount + ($amount * $rubric->taxe), 2),
                    'taxe' => $rubric->taxe
                ];
            }

            $services_amounts[$rubric->title] = $amount;
            $total_ht += $amount['ht'];
            $total_tva += $amount['tva'];
            $total_ttc += $amount['ttc'];
        }
        $res_amount = [
            'total' => ['ht' => $total_ht, 'tva' => $total_tva, 'ttc' => $total_ttc],
            'services' => $services_amounts,
            'tranches' => $tranches,
        ];

        Log::info('tranches' . json_encode($tranches));

        self::after($res_amount, $payload, $rejected_rubrics);


        if (isset($id_exp)) {



            $taxer_id = Auth::user()->id ?? null;

            // if (!$taxer_id) return;

            // create or update recipesse 

            if ($payload['mode_port']->title == 'PP' || $payload['mode_port']->title == 'PD') {

                $recipet = Receipet::updateOrCreate(
                    ['expedition_id' => $id_exp],
                    [
                        'taxer_id' => $taxer_id,
                        'driver_id' => $driver_id,
                    ]
                );

                foreach ($res_amount['services'] as $key => $service) {
                    ReceipetTarifDetails::create([
                        'service' => $key,
                        'ht' => $service['ht'],
                        'tva' => $service['tva'],
                        'ttc' => $service['ttc'],
                        'taxe' => $service['taxe'],
                        'receipet_id' => $recipet->id,
                    ]);
                }

                // return;
            }


            $id_client = null;
            if ($payload['mode_port']->title == 'PPE') {
                $id_client = $payload['id_customer'];
            } else if ($payload['mode_port']->title == 'PDE') {
                $id_client = $payload['id_receiver'];
            }

            // if (!$id_client) {
            //     return;
            // }


            Log::info('id_exp' . json_encode($id_exp));
            ExpeditionTarifDetails::where('expedition_id', $id_exp)->delete();




            if ($payload['mode_port']->title != 'PP' || $payload['mode_port']->title != 'PD') {

                foreach ($res_amount['services'] as $key => $service) {

                    Log::info('service in tarification' . json_encode($service));
    
                    ExpeditionTarifDetails::create([
                        'service' => $key,
                        'ht' => $service['ht'],
                        'tva' => $service['tva'],
                        'ttc' => $service['ttc'],
                        'taxe' => $service['taxe'],
                        'expedition_id' => $id_exp,
                        'customer_id' => $id_client
                    ]);
                }
            }
         


        }
        return $this->mainResponse(eRespCode::_200_TA_OK, true, $res_amount);
        ;
    }

    public function lookup(&$payload, &$rejected_rubrics)
    {



        if ($payload['id_customer'] != -1 && !$payload['client']['is_active']) {
            return $this->mainResponse(eRespCode::_400_BAD_REQUEST, false, 'ce compte client est désactivé');
        }


        // exclude gms if is false 
        if ($payload['id_receiver'] != -1 && !$payload['receiver']['GMS']) {

            $filtred_rubrics = $payload['rubrics']->reject(function ($item) {
                return $item->title == 'GMS';
            });

            $rejected_rubrics[] = $payload['rubrics']->diff($filtred_rubrics)[0];

            $payload['rubrics'] = $filtred_rubrics;

        }

        // exclude Liv Dom when if type_livraison is not domicile
        if ($payload['type_livraison'] != 'domicile') {

            $filtred_rubrics = $payload['rubrics']->reject(function ($item) {
                return $item->title == 'LIV DOM';
            });

            $rejected_rubrics[] = $payload['rubrics']->diff($filtred_rubrics)[0];

            $payload['rubrics'] = $filtred_rubrics;

        }


        //TODO: validation to chek if login customer is an agent
        // exclude ramsaage if is agency and rammsseur_id == null
        // if ($payload['is_agency'] &&  !$payload['ramasseur_id']) {


        //     $filtred_rubrics = $payload['rubrics']->reject(function($item) {
        //         return $item->title == 'RAMASSAGE';
        //     });

        //     $rejected_rubrics[] =  $payload['rubrics']->diff($filtred_rubrics)[0];

        //     $payload['rubrics'] = $filtred_rubrics;

        // }

        if ($payload['id_receiver'] != -1 && $payload['id_customer'] != -1 && $payload['mode_port']->for_receive) {

            // Debug logging
            Log::info('Debug payload data:', [
                'id_receiver' => $payload['id_receiver'],
                'id_customer' => $payload['id_customer'],
                'mode_port' => $payload['mode_port'],
                'receiver_data' => $payload['receiver']
            ]);

            // fetch receiver authorized_customers
            $authorized_customers_in_send = $payload['receiver']['authorized_customers_in_send'];

            Log::info('authorized_customers_in_send', [
                'raw' => $authorized_customers_in_send,
                'type' => gettype($authorized_customers_in_send),
                'is_collection' => $authorized_customers_in_send instanceof \Illuminate\Support\Collection
            ]);

            if (!$payload['receiver']['sender_priority'] && $payload['receiver']['receiver_priority']) {
                Log::info('Priority check passed', [
                    'sender_priority' => $payload['receiver']['sender_priority'],
                    'receiver_priority' => $payload['receiver']['receiver_priority']
                ]);
                
                $sender_is_authorized = $authorized_customers_in_send->contains(function ($authorized_customer) use ($payload) {
                    Log::info('Checking authorization', [
                        'authorized_id' => $authorized_customer->id ?? 'null',
                        'customer_id' => $payload['id_customer']
                    ]);
                    return $authorized_customer->id == $payload['id_customer'];
                });

                Log::info('sender_is_authorized result', ['value' => $sender_is_authorized]);

                if ($sender_is_authorized) {
                    Log::info('Swapping sender and receiver', [
                        'before_swap' => [
                            'id_receiver' => $payload['id_receiver'],
                            'id_customer' => $payload['id_customer']
                        ]
                    ]);
                    
                    // Swap sender_id and receiver_id
                    [$payload['id_receiver'], $payload['id_customer']] = [$payload['id_customer'], $payload['id_receiver']];
                    
                    // Update client and receiver data after swap
                    $temp = $payload['client'];
                    $payload['client'] = $payload['receiver'];
                    $payload['receiver'] = $temp;
                    
                    Log::info('After swap', [
                        'id_receiver' => $payload['id_receiver'],
                        'id_customer' => $payload['id_customer'],
                        'client_id' => $payload['client']['id'],
                        'receiver_id' => $payload['receiver']['id']
                    ]);

                }
            }
        }
    }

    public static function after(&$montant, $payload, $rejected_rubrics)
    {
        $PS_TVA = GlobalConfig::where('type', eConfigGlobal::_PS_TVA)->first()->valeur;
        $ENC_TVA = GlobalConfig::where('type', eConfigGlobal::_ENC_TVA)->first()->valeur;

        // Check if customer is subject to TVA
        $isSubjectToTVA = isset($payload['client']['subject_to_TVA']) ? $payload['client']['subject_to_TVA'] : true;
        
        // If customer is not subject to TVA, set all TVA values to 0
        if (!$isSubjectToTVA) {
            foreach ($montant['services'] as $key => $service) {
                $montant['services'][$key]['tva'] = 0;
                $montant['services'][$key]['taxe'] = 0;
                $montant['services'][$key]['ttc'] = $montant['services'][$key]['ht'];
            }
            $montant['total']['tva'] = 0;
            $montant['total']['ttc'] = $montant['total']['ht'];
        }

        //TODO: validation to chek if login customer is an agent
        // PS
        if ($payload['is_agency'] && $payload['PS']) {
            $amount = $payload['PS'];
            $tva_amount = $isSubjectToTVA ? round($amount * $PS_TVA, 2) : 0;
            $ttc_amount = $isSubjectToTVA ? round($amount + ($amount * $PS_TVA), 2) : $amount;
            
            $montant['services']['PS'] = [
                'ht' => $amount,
                'tva' => $tva_amount,
                'ttc' => $ttc_amount,
                'taxe' => $isSubjectToTVA ? $PS_TVA : 0
            ];

            $montant['total']['ht'] += $montant['services']['PS']['ht'];
            $montant['total']['tva'] += $montant['services']['PS']['tva'];
            $montant['total']['ttc'] += $montant['services']['PS']['ttc'];
        }

        if ($payload['is_agency'] && $payload['encombrement']) {
            $transport_ttc = $montant['services']['TRANSPORT']['ttc'];
            $encombrement_pourcentage = $payload['encombrement'];
            $ht_amount = round($transport_ttc * $encombrement_pourcentage, 2);
            $tva_amount = $isSubjectToTVA ? round($transport_ttc * $encombrement_pourcentage * $ENC_TVA, 2) : 0;
            $ttc_amount = $isSubjectToTVA ? 
                round($transport_ttc * $encombrement_pourcentage + ($transport_ttc * $encombrement_pourcentage * $ENC_TVA), 2) : 
                $ht_amount;
            
            $montant['services']['encombrement'] = [
                'ht' => $ht_amount,
                'tva' => $tva_amount,
                'ttc' => $ttc_amount,
                'taxe' => $isSubjectToTVA ? $ENC_TVA : 0
            ];

            $montant['total']['ht'] += $montant['services']['encombrement']['ht'];
            $montant['total']['tva'] += $montant['services']['encombrement']['tva'];
            $montant['total']['ttc'] += $montant['services']['encombrement']['ttc'];
        }

        $montant['total']['ttc'] = round($montant['total']['ttc']);
        $montant['total']['tva'] = round($montant['total']['tva']);
        $montant['total']['ht'] = round($montant['total']['ht']);
        
        // set rejected rejected_rubrics values to zero
        foreach ($rejected_rubrics as $value) {
            Log::info('rejected rubrics' . json_encode($value));
            $amount = 0;
            $montant['services'][$value->title] = [
                'ht' => 0,
                'tva' => 0,
                'ttc' => 0,
                'taxe' => 0
            ];
        }

        $montant['services'] = array_reverse($montant['services']);
    }

}
