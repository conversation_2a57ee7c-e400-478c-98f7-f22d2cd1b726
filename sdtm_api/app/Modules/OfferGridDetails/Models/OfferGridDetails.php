<?php

namespace App\Modules\OfferGridDetails\Models;

use App\Traits\HasUuid;
use App\Traits\Filterable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Modules\ProductCategory\Models\ProductCategory;
use App\Modules\CalculBasis\Models\CalculBasis;
use App\Modules\Rubric\Models\Rubric;
use App\Modules\ReturnDocument\Models\ReturnDocument;
use App\Traits\HasHorodatage;

class OfferGridDetails extends Model
{
    use HasFactory, HasUuid, Filterable, HasHorodatage;

    protected $table = 'offer_grid_details';
    protected $primaryKey = 'id';

    protected $fillable = [
        'calcul_basis_id', 
        'originable_type',
        'originable_id',
        'destinationable_type',
        'destinationable_id',
        'minmax_calcul_basis_id',
        'prod_category_id',
        'type_val',
        'calcul_val',
        'u_sup',
        'val_sup',
        'min', 
        'max',
        'cond_basis_id',
        'min_val',
        'max_val',
        'max_poids',
        'offerable_id',
        'offerable_type',
        'rubric_id',
        'sup_calcul_basis_id',
        'type_product_category',
        'parent_id',
        'type_parent',
        'min_by_package'
    ];


     /**
     * Get the parent originable model (class or ville).
     */
    public function originable()
    {
        return $this->morphTo();
    }

    /**
     * Get the parent destinationable model (class or ville).
     */
    public function destinationable()
    {
        return $this->morphTo();
    }

    /**
     * Get the parent offerable model (class or ville).
     */
    public function offerable()
    {
        return $this->morphTo();
    }

    public function rubric() {
        return $this->hasOne(Rubric::class, 'id', 'rubric_id');
    }

    public function basis_calcul() {
        return $this->hasOne(CalculBasis::class, 'id', 'calcul_basis_id');
    }

    public function sup_basis_calcul() {
        return $this->hasOne(CalculBasis::class, 'id', 'sup_calcul_basis_id');
    }

    public function max_basis_calcul() {
        return $this->hasOne(CalculBasis::class, 'id', 'minmax_calcul_basis_id');
    }

    public function categorie_produit() {
        return $this->hasOne(ProductCategory::class, 'id', 'prod_category_id');
    }

    public function return_documents() {
        return $this->hasMany(ReturnDocument::class, 'id_expedition', 'id');
    }

    /**
     * Scope for filtering by location (origin and destination)
     */
    public function scopeByLocation($query, $originCityId = null, $destCityId = null)
    {
        return $query->where(function($q) use ($destCityId) {
            $q->whereNull('destinationable_id');
            if ($destCityId) {
                $q->orWhere('destinationable_id', $destCityId);
            }
        })->where(function($q) use ($originCityId) {
            $q->whereNull('originable_id');
            if ($originCityId) {
                $q->orWhere('originable_id', $originCityId);
            }
        });
    }

    /**
     * Scope for filtering by rubric
     */
    public function scopeByRubric($query, $rubricId)
    {
        return $query->where('rubric_id', $rubricId);
    }

    /**
     * Scope for filtering by product category
     */
    public function scopeByProductCategory($query, $productCategory)
    {
        return $query->where('type_product_category', $productCategory);
    }

    /**
     * Scope for optimized tarification queries
     */
    public function scopeForTarification($query, $originCityId = null, $destCityId = null, $rubricId = null)
    {
        $query = $query->select([
            'id', 'offerable_id', 'offerable_type', 'rubric_id',
            'calcul_basis_id', 'sup_calcul_basis_id', 'destinationable_id',
            'originable_id', 'type_val', 'calcul_val', 'min', 'max',
            'min_val', 'max_val', 'type_product_category'
        ]);

        if ($originCityId || $destCityId) {
            $query->byLocation($originCityId, $destCityId);
        }

        if ($rubricId) {
            $query->byRubric($rubricId);
        }

        return $query->with([
            'destinationable:id,title',
            'basis_calcul:id,title',
            'sup_basis_calcul:id,title'
        ]);
    }

}
